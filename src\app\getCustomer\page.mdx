export const metadata = {
  title: 'GET Customer',
  description:
    'On this page, we’ll dive into the different customers endpoints you can use to manage customers programmatically.',
}

# Clientes

Los clientes son una parte esencial de la plataforma. Este endpoint te permite **consultar la información de un cliente específico mediante su ID** de manera programática. Aquí podrás recuperar los datos registrados de un cliente en el sistema.

## El modelo de cliente

El modelo de cliente contiene toda la información relevante acerca de un cliente, como su nombre, dirección, información de contacto y detalles sobre su ocupación y estado legal. Esta información puede ser consultada utilizando el ID único del cliente.

---

# Endpoints disponibles de Customer/Cliente
 
## Customer Search {{ tag: 'GET', label: '/customer?id=' }}

<Row>
  <Col>

    Este endpoint devuelve los detalles de un customer.

    ### Required attributes

    <Properties>
      <Property name="customerID" type="string">
        ID del cliente.
      </Property>
    </Properties>

  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="GET" label="/customer?id=">

    ```bash {{ title: 'cURL' }}
      curl --location 'https://ztr3w5aj41.execute-api.us-east-1.amazonaws.com/QA/customer?id=100006' \
      --header 'x-api-key: xXlqWn4cPp9IQhkjVFkgbvJeauo9rN719dduKX80' \
      --header 'T24-Token: {{T24-TOKEN}}'
    ```

    ```js
  const myHeaders = new Headers();
    myHeaders.append("x-api-key", "xXlqWn4cPp9IQhkjVFkgbvJeauo9rN719dduKX80");
    myHeaders.append("T24-Token", "{{T24-TOKEN}}");

    const requestOptions = {
      method: "GET",
      headers: myHeaders,
      redirect: "follow"
    };

    fetch("https://ztr3w5aj41.execute-api.us-east-1.amazonaws.com/QA/customer?id=100006", requestOptions)
      .then((response) => response.text())
      .then((result) => console.log(result))
      .catch((error) => console.error(error));
    ```

    ```python
    import requests

    url = "https://ztr3w5aj41.execute-api.us-east-1.amazonaws.com/QA/customer?id=100006"

    payload = {}
    headers = {
      'x-api-key': 'xXlqWn4cPp9IQhkjVFkgbvJeauo9rN719dduKX80',
      'T24-Token': '{{T24-TOKEN}}'
    }
    response = requests.request("GET", url, headers=headers, data=payload)
    print(response.text)
    ```
    </CodeGroup>

  </Col>
</Row>

  ---
  # Seguridad y Configuración del Endpoint GET /customer

  Esta sección describe cómo se ha configurado el **API Gateway** que expone el endpoint de obtención de clientes por ID, garantizando un manejo seguro, eficiente y controlado de las solicitudes entrantes.

  ---

  ## 🔐 Seguridad: CORS y Encabezados Personalizados

  ### ✅ Habilitación de CORS

  Para permitir el consumo del API desde aplicaciones web o herramientas externas, se habilita **CORS** en el método `GET`:

  - `Access-Control-Allow-Origin: BPM`  
    (limitado a nuestro entorno productivo de nuestro BPM).
  - `Access-Control-Allow-Methods: GET, OPTIONS`
  - `Access-Control-Allow-Headers: Content-Type, Authorization, T24-Token, x-api-key`

  Esto permite que navegadores modernos interactúen con el API sin restricciones innecesarias, respetando los estándares web.

  ---

  ### 🧾 Encabezados Personalizados

  Para reforzar la seguridad y trazabilidad, se procesan y validan los siguientes encabezados:

  - `T24-Token`: Token requerido para autenticar contra el core bancario.
  - `x-api-key`: Clave de API administrada desde API Gateway.
  - `User-Agent`, `Accept`, `Host`: Información básica de cliente.
  - `X-Forwarded-For`, `X-Forwarded-Port`, `X-Forwarded-Proto`: Información de red para auditoría.
  - `X-Amzn-Trace-Id`: ID de trazabilidad generado por AWS.

  ---

  ## ⚙️ Plantilla de Integración (Mapping Template)

  Para proteger la función Lambda y controlar la entrada de datos, se usa una plantilla personalizada en formato **VTL (Velocity Template Language)**. Esta plantilla transforma la solicitud HTTP en un JSON estructurado, asegurando que solo se transmita la información permitida.

  ### 🧩 Plantilla de ejemplo – GET /customer

  ```json
  {
    "queryStringParameters": {
      #if($input.params().querystring.id)
      "id": "$input.params().querystring.id"
      #end
    },
    # en el caso de GET , no lleva body
    "headers": {
      "Accept": "$input.params().header.Accept",
      "Accept-Encoding": "$input.params().header.Accept-Encoding",
      "Cache-Control": "$input.params().header.Cache-Control",
      "Host": "$input.params().header.Host",
      "User-Agent": "$input.params().header.User-Agent",
      "X-Amzn-Trace-Id": "$input.params().header.X-Amzn-Trace-Id",
      "x-api-key": "$input.params().header.x-api-key",
      "T24-Token": "$input.params().header.T24-Token",
      "X-Forwarded-For": "$context.identity.sourceIp",
      "X-Forwarded-Port": "$context.identity.sourcePort",
      "X-Forwarded-Proto": "$context.protocol"
    }
  }
  ```
---

  ## ✅ Respuesta Exitosa

```json {{ title: 'Response' }}
          {
            {
        "statusCode": 200,
        "headers": {
          "transactionStatus": "Held",
          "audit": {
            "T24_time": 1250,
            "responseParse_time": 5,
            "requestParse_time": 400,
            "versionNumber": "17"
          },
          "id": "100001",
          "status": "success"
        },
        "body": {
          "customerMnemonic": "PR100001",
          "manualRiskClass": "MR",
          "lastName": "PÉREZ LOPEZ",
          "addresses": [
            {
              "address": "CALLE 50, EDIFICIO TORRE GLOBAL"
            }
          ],
          "numberOfDependents": 3,
          "gender": "MALE",
          "streets": [
            {
              "street": "VÍA ESPAÑA"
            }
          ],
          "contactDate": "2012-05-14",
          "faxIds": [
            {
              "faxId": "5071234567"
            }
          ],
          "customerNames": [
            {
              "customerName": "JUAN CARLOS PÉREZ LOPEZ"
            }
          ],
          "language": 1,
          "title": "MR",
          "kycLastReviewDate": "2024-01-10",
          "communicationDevices": [
            {
              "phoneNumber": "3900123",
              "smsNumber": "********",
              "email": "<EMAIL>"
            }
          ],
          "customerStatus": 1,
          "industryId": 501,
          "accountOfficerId": 2001,
          "officePhoneNumbers": [
            {
              "officePhoneNumber": "3900456"
            }
          ],
          "customerCompany": "PA0011234",
          "amlResult": "CLEAR",
          "isKycComplete": true,
          "employDetails": [
            {
              "occupation": "CONTADOR",
              "salaryCurrency": "USD",
              "employerName": "GRUPO FINANCIERO DEL ISTMO",
              "employStatus": "EMPLOYED"
            }
          ],
          "sectorId": 1001,
          "nationalityId": "PA",
          "givenName": "JUAN CARLOS",
          "addressCities": [
            {
              "addressCity": "PANAMÁ"
            }
          ],
          "displayNames": [
            {
              "displayName": "JUAN CARLOS PÉREZ LOPEZ"
            }
          ],
          "dateOfBirth": "1985-11-15",
          "countries": [
            {
              "country": "PA"
            }
          ],
          "amlCheck": "CLEAR",
          "residenceId": "PA",
          "target": 7,
          "domicile": "PA",
          "postCode": "0801-00123",
          "maritalStatus": "SINGLE",
          "legalDetails": [
            {
              "legalIssueDate": "2016-03-01",
              "legalExpiredDate": "2026-03-01",
              "legalId": "08-00-00001-000001",
              "legalDocumentName": "CEDULA",
              "legalIssueAuthorisedDate": "PANAMA",
              "legalHolderName": "JUAN CARLOS PÉREZ LOPEZ"
            }
          ]
        }
      }
    }
    ```