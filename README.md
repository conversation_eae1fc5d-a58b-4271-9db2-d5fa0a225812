# Documentación de APIs AWS

## Descripción del Proyecto
Este proyecto en **Next.js** tiene como objetivo documentar la arquitectura y el uso de **Integradoc** en la automatización de procesos bancarios. La documentación se enfoca en la integración con sistemas bancarios, la seguridad en AWS y las mejores prácticas para gestionar flujos de trabajo en **Integradoc**.

## Tecnologías Utilizadas
- **Next.js** con `output: "export"` para generar un sitio estático
- **Integradoc BPM** para gestión de procesos
- **Temenos Transact** como core bancario
- **API Gateway & Lambdas** para integración con AWS

## Arquitectura de la Solución
El diagrama de arquitectura detalla la interconexión de los diferentes componentes dentro de AWS y su integración con Integradoc. Los principales componentes son:

- **VPC-1**: Contiene servicios internos de Integradoc como base de datos (PostgreSQL), Elasticsearch y microservicios.
- **VPC-2**: Aloja el servidor de aplicación de Integradoc y el balanceador de carga (ALB) que gestiona el tráfico.
- **VPNs y Fortinet**: Permiten la comunicación segura con la infraestructura del banco y Temenos Cloud.
- **API Gateway**: Actúa como intermediario entre los clientes y las funciones Lambda para la generación de JWT y consumo de APIs de Temenos.
- **CloudWatch & Shield**: Se encargan de la seguridad y monitoreo de la infraestructura.

## Uso de Integradoc en Procesos Bancarios
Integradoc se emplea para automatizar y gestionar procesos bancarios, permitiendo:
- **Gestiones de aprobaciones** para solicitudes de crédito, aperturas de cuentas y otros servicios.
- **Cumplimiento regulatorio**, asegurando el seguimiento de normativas bancarias.
- **Integración con Temenos Transact** para registrar y actualizar información financiera en tiempo real.
- **Monitoreo y optimización** de procesos mediante dashboards y reportes.

## Instalación y Despliegue
1. Clonar el repositorio:
   ```sh
   git clone <repo_url>
   cd <repo>
   ```
2. Instalar dependencias:
   ```sh
   npm install
   ```
3. Generar la versión estática:
   ```sh
   npm run build
   ```

## Contacto y Soporte
Para dudas o mejoras en la documentación, contactar con el equipo de arquitectura de Grupo Prival.

