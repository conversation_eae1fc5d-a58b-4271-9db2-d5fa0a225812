@import 'tailwindcss';
@plugin '@tailwindcss/typography';
@config '../../typography.ts';
@custom-variant dark (&:where(.dark, .dark *));

@theme {
  --text-2xs: 0.75rem;
  --text-2xs--line-height: 1.25rem;
  --text-xs: 0.8125rem;
  --text-xs--line-height: 1.5rem;
  --text-sm: 0.875rem;
  --text-sm--line-height: 1.5rem;
  --text-base: 1rem;
  --text-base--line-height: 1.75rem;
  --text-lg: 1.125rem;
  --text-lg--line-height: 1.75rem;
  --text-xl: 1.25rem;
  --text-xl--line-height: 1.75rem;
  --text-2xl: 1.5rem;
  --text-2xl--line-height: 2rem;
  --text-3xl: 1.875rem;
  --text-3xl--line-height: 2.25rem;
  --text-4xl: 2.25rem;
  --text-4xl--line-height: 2.5rem;
  --text-5xl: 3rem;
  --text-5xl--line-height: 1;
  --text-6xl: 3.75rem;
  --text-6xl--line-height: 1;
  --text-7xl: 4.5rem;
  --text-7xl--line-height: 1;
  --text-8xl: 6rem;
  --text-8xl--line-height: 1;
  --text-9xl: 8rem;
  --text-9xl--line-height: 1;

  --shadow-glow: 0 0 4px rgb(0 0 0 / 0.1);

  --container-lg: 33rem;
  --container-2xl: 40rem;
  --container-3xl: 50rem;
  --container-5xl: 66rem;
}

@layer base {
  :root {
    --shiki-color-text: var(--color-white);
    --shiki-token-constant: var(--color-orange-600);
    --shiki-token-string: var(--color-orange-600);
    --shiki-token-comment: var(--color-zinc-500);
    --shiki-token-keyword: var(--color-sky-300);
    --shiki-token-parameter: var(--color-pink-300);
    --shiki-token-function: var(--color-violet-300);
    --shiki-token-string-expression: var(--color-orange-600);
    --shiki-token-punctuation: var(--color-zinc-200);
  }

  [inert] ::-webkit-scrollbar {
    display: none;
  }
}
