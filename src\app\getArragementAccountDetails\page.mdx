export const metadata = {
  title: 'GET Customer',
  description:
    'On this page, we’ll dive into the different arragement endpoints you can use to manage arragement programmatically.',
}

# Cuentas - Consulta de Detalles

La consulta de cuentas es una funcionalidad clave del sistema que permite recuperar la información detallada de todas las cuentas asociadas a un cliente específico, utilizando su identificador único (CIF). Este endpoint está orientado a **obtener los detalles de los productos bancarios contratados por el cliente**, tales como cuentas corrientes, de ahorro, depósitos y otros instrumentos financieros.

## Consulta de (`arrangementAccountDetails`)

Este endpoint permite recuperar de forma programática los detalles completos de cada cuenta vinculada al cliente

---

# Endpoints disponibles de ArrangementAccountDetails
 
## ArrangementAccountDetails Search {{ tag: 'GET', label: '/arrangement_account/details?id=' }}

<Row>
  <Col>

    Este endpoint devuelve los detalles de las cuentas.

    ### Required attributes

    <Properties>
      <Property name="customerID" type="string">
        ID del cliente.
      </Property>
    </Properties>

  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="GET" label="/customer?id=">

    ```bash {{ title: 'cURL' }}
      curl --location 'https://ztr3w5aj41.execute-api.us-east-1.amazonaws.com/QA/arragement_account/details?id=109569' \
      --header 'T24-Token: {{T24-Token}}' \
      --header 'x-api-key: xXlqWn4cPp9IQhkjVFkgbvJeauo9rN719dduKX80'
    ```

    ```js
      const myHeaders = new Headers();
      myHeaders.append("T24-Token", "{{T24-Token}}");
      myHeaders.append("x-api-key", "xXlqWn4cPp9IQhkjVFkgbvJeauo9rN719dduKX80");

      const requestOptions = {
        method: "GET",
        headers: myHeaders,
        redirect: "follow"
      };

      fetch("https://ztr3w5aj41.execute-api.us-east-1.amazonaws.com/QA/arragement_account/details?id=109569", requestOptions)
        .then((response) => response.text())
        .then((result) => console.log(result))
        .catch((error) => console.error(error));
    ```

    ```python
      import requests

      url = "https://ztr3w5aj41.execute-api.us-east-1.amazonaws.com/QA/arragement_account/details?id=109569"

      payload = {}
      headers = {
        'T24-Token': '{{T24-Token}}',
        'x-api-key': 'xXlqWn4cPp9IQhkjVFkgbvJeauo9rN719dduKX80'
      }

      response = requests.request("GET", url, headers=headers, data=payload)

      print(response.text)
    ```
    </CodeGroup>

  </Col>
</Row>

  ---
  # Seguridad y Configuración del Endpoint GET /arrangement_account/details

  Esta sección describe cómo se ha configurado el **API Gateway** que expone el endpoint de obtención de productos bancarios por ID de cliente, garantizando un manejo seguro, eficiente y controlado de las solicitudes entrantes.

  ---

  ## 🔐 Seguridad: CORS y Encabezados Personalizados

  ### ✅ Habilitación de CORS

  Para permitir el consumo del API desde aplicaciones web o herramientas externas, se habilita **CORS** en el método `GET`:

  - `Access-Control-Allow-Origin: BPM`  
    (limitado a nuestro entorno productivo de nuestro BPM).
  - `Access-Control-Allow-Methods: GET, OPTIONS`
  - `Access-Control-Allow-Headers: Content-Type, Authorization, T24-Token, x-api-key`

  Esto permite que navegadores modernos interactúen con el API sin restricciones innecesarias, respetando los estándares web.

  ---

  ### 🧾 Encabezados Personalizados

  Para reforzar la seguridad y trazabilidad, se procesan y validan los siguientes encabezados:

  - `T24-Token`: Token requerido para autenticar contra el core bancario.
  - `x-api-key`: Clave de API administrada desde API Gateway.
  - `User-Agent`, `Accept`, `Host`: Información básica de cliente.
  - `X-Forwarded-For`, `X-Forwarded-Port`, `X-Forwarded-Proto`: Información de red para auditoría.
  - `X-Amzn-Trace-Id`: ID de trazabilidad generado por AWS.

  ---

  ## ⚙️ Plantilla de Integración (Mapping Template)

  Para proteger la función Lambda y controlar la entrada de datos, se usa una plantilla personalizada en formato **VTL (Velocity Template Language)**. Esta plantilla transforma la solicitud HTTP en un JSON estructurado, asegurando que solo se transmita la información permitida.

  ### 🧩 Plantilla de ejemplo – GET / arrangement account details

  ```json
  {
    "queryStringParameters": {
      #if($input.params().querystring.id)
      "id": "$input.params().querystring.id"
      #end
    },
    # en el caso de GET , no lleva body
    "headers": {
      "Accept": "$input.params().header.Accept",
      "Accept-Encoding": "$input.params().header.Accept-Encoding",
      "Cache-Control": "$input.params().header.Cache-Control",
      "Host": "$input.params().header.Host",
      "User-Agent": "$input.params().header.User-Agent",
      "X-Amzn-Trace-Id": "$input.params().header.X-Amzn-Trace-Id",
      "x-api-key": "$input.params().header.x-api-key",
      "T24-Token": "$input.params().header.T24-Token",
      "X-Forwarded-For": "$context.identity.sourceIp",
      "X-Forwarded-Port": "$context.identity.sourcePort",
      "X-Forwarded-Proto": "$context.protocol"
    }
  }
  ```
---

  ## ✅ Respuesta Exitosa

```json {{ title: 'Response' }}
          {
            {
        "statusCode": 200,
        "headers": {
          "transactionStatus": "Held",
          "audit": {
            "T24_time": 1250,
            "responseParse_time": 5,
            "requestParse_time": 400,
            "versionNumber": "17"
          },
          "id": "******",
          "status": "success"
        },
        "body": {
          "data": "*****"
        }
      }
    }
    ```