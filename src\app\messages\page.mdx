export const metadata = {
  title: 'Messages',
  description:
    'On this page, we’ll dive into the different message endpoints you can use to manage messages programmatically.',
}

# Messages

Messages are what conversations are made of in Protocol — they are the basic building blocks of your conversations with your Protocol contacts. On this page, we'll dive into the different message endpoints you can use to manage messages programmatically. We'll look at how to query, send, update, and delete messages. {{ className: 'lead' }}

## The message model

The message model contains all the information about the messages and attachments you send to your contacts and groups, including how your contacts have reacted to them.

### Properties

<Properties>
  <Property name="id" type="string">
    Unique identifier for the message.
  </Property>
  <Property name="conversation_id" type="string">
    Unique identifier for the conversation the message belongs to.
  </Property>
  <Property name="contact" type="object">
    The contact object for the contact who sent the message.
  </Property>
  <Property name="message" type="string">
    The message content.
  </Property>
  <Property name="reactions" type="array">
    An array of reaction objects associated with the message.
  </Property>
  <Property name="attachments" type="array">
    An array of attachment objects associated with the message.
  </Property>
  <Property name="read_at" type="timestamp">
    Timestamp of when the message was read.
  </Property>
  <Property name="created_at" type="timestamp">
    Timestamp of when the message was created.
  </Property>
  <Property name="updated_at" type="timestamp">
    Timestamp of when the message was last updated.
  </Property>
</Properties>

---

## List all messages {{ tag: 'GET', label: '/v1/messages' }}

<Row>
  <Col>

    This endpoint allows you to retrieve a paginated list of all your messages (in a conversation if a conversation id is provided). By default, a maximum of ten messages are shown per page.

    ### Optional attributes

    <Properties>
      <Property name="conversation_id" type="string">
        Limit to messages from a given conversation.
      </Property>
      <Property name="limit" type="integer">
        Limit the number of messages returned.
      </Property>
    </Properties>

  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="GET" label="/v1/messages">

    ```bash {{ title: 'cURL' }}
    curl -G https://api.protocol.chat/v1/messages \
      -H "Authorization: Bearer {token}" \
      -d conversation_id=xgQQXg3hrtjh7AvZ \
      -d limit=10
    ```

    ```js
    import ApiClient from '@example/protocol-api'

    const client = new ApiClient(token)

    await client.messages.list()
    ```

    ```python
    from protocol_api import ApiClient

    client = ApiClient(token)

    client.messages.list()
    ```

    ```php
    $client = new \Protocol\ApiClient($token);

    $client->messages->list();
    ```

    </CodeGroup>

    ```json {{ title: 'Response' }}
    {
      "has_more": false,
      "data": [
        {
          "id": "SIuAFUNKdSYHZF2w",
          "conversation_id": "xgQQXg3hrtjh7AvZ",
          "contact": {
            "id": "WAz8eIbvDR60rouK",
            "username": "KevinMcCallister",
            "phone_number": "**************",
            "avatar_url": "https://assets.protocol.chat/avatars/buzzboy.jpg",
            "last_active_at": *********,
            "created_at": *********
          },
          "message": "It’s a nice night for a neck injury.",
          "reactions": [],
          "attachments": [],
          "read_at": *********,
          "created_at": *********,
          "updated_at": *********
        },
        {
          "id": "hSIhXBhNe8X1d8Et",
          // ..
        }
      ]
    }
    ```

  </Col>
</Row>

---

## Send a message {{ tag: 'POST', label: '/v1/messages' }}

<Row>
  <Col>

    This endpoint allows you to send a new message to one of your conversations.

    ### Required attributes

    <Properties>
      <Property name="conversation_id" type="string">
        Unique identifier for the conversation the message belongs to.
      </Property>
      <Property name="message" type="string">
        The message content.
      </Property>
    </Properties>

    ### Optional attributes

    <Properties>
      <Property name="attachments" type="array">
        An array of attachment objects associated with the message.
      </Property>
    </Properties>

  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="POST" label="/v1/messages">

    ```bash {{ title: 'cURL' }}
    curl https://api.protocol.chat/v1/messages \
      -H "Authorization: Bearer {token}" \
      -d conversation_id="xgQQXg3hrtjh7AvZ" \
      -d message="You’re what the French call ‘les incompetents.’"
    ```

    ```js
    import ApiClient from '@example/protocol-api'

    const client = new ApiClient(token)

    await client.messages.send({
      conversation_id: 'xgQQXg3hrtjh7AvZ',
      message: 'You’re what the French call ‘les incompetents.’',
    })
    ```

    ```python
    from protocol_api import ApiClient

    client = ApiClient(token)

    client.messages.send(
      conversation_id="xgQQXg3hrtjh7AvZ",
      message="You’re what the French call ‘les incompetents.’",
    )
    ```

    ```php
    $client = new \Protocol\ApiClient($token);

    $client->messages->send([
      'conversation_id' => 'xgQQXg3hrtjh7AvZ',
      'message' => 'You’re what the French call ‘les incompetents.’',
    ]);
    ```

    </CodeGroup>

    ```json {{ title: 'Response' }}
    {
      "id": "gWqY86BMFRiH5o11",
      "conversation_id": "xgQQXg3hrtjh7AvZ",
      "contact": {
        "id": "inEIRvzjC6YLMX3o",
        "username": "LinnieMcCallister",
        "phone_number": "**************",
        "avatar_url": "https://assets.protocol.chat/avatars/linnie.jpg",
        "last_active_at": *********,
        "created_at": *********
      },
      "message": "You’re what the French call ‘les incompetents.’",
      "reactions": [],
      "attachments": [],
      "read_at": null,
      "created_at": *********,
      "updated_at": null
    }
    ```

  </Col>
</Row>

---

## Retrieve a message {{ tag: 'GET', label: '/v1/messages/:id' }}

<Row>
  <Col>

    This endpoint allows you to retrieve a message by providing the message id. Refer to [the list](#the-message-model) at the top of this page to see which properties are included with message objects.

  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="GET" label="/v1/messages/SIuAFUNKdSYHZF2w">

    ```bash {{ title: 'cURL' }}
    curl https://api.protocol.chat/v1/messages/SIuAFUNKdSYHZF2w \
      -H "Authorization: Bearer {token}"
    ```

    ```js
    import ApiClient from '@example/protocol-api'

    const client = new ApiClient(token)

    await client.messages.get('SIuAFUNKdSYHZF2w')
    ```

    ```python
    from protocol_api import ApiClient

    client = ApiClient(token)

    client.messages.get("SIuAFUNKdSYHZF2w")
    ```

    ```php
    $client = new \Protocol\ApiClient($token);

    $client->messages->get('SIuAFUNKdSYHZF2w');
    ```

    </CodeGroup>

    ```json {{ title: 'Response' }}
    {
      "id": "SIuAFUNKdSYHZF2w",
      "conversation_id": "xgQQXg3hrtjh7AvZ",
      "contact": {
        "id": "WAz8eIbvDR60rouK",
        "username": "KevinMcCallister",
        "phone_number": "**************",
        "avatar_url": "https://assets.protocol.chat/avatars/kevin.jpg",
        "last_active_at": *********,
        "created_at": *********
      },
      "message": "I’m traveling with my dad. He’s at a meeting. I hate meetings.",
      "reactions": [],
      "attachments": [],
      "read_at": *********,
      "created_at": *********,
      "updated_at": *********
    }
    ```

  </Col>
</Row>

---

## Update a message {{ tag: 'PUT', label: '/v1/messages/:id' }}

<Row>
  <Col>

    This endpoint allows you to perform an update on a message. Examples of updates are adding a reaction, editing the message, or adding an attachment.

    ### Optional attributes

    <Properties>
      <Property name="message" type="string">
        The message content.
      </Property>
      <Property name="reactions" type="array">
        An array of reaction objects associated with the message.
      </Property>
      <Property name="attachments" type="array">
        An array of attachment objects associated with the message.
      </Property>
    </Properties>

  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="PUT" label="/v1/messages/SIuAFUNKdSYHZF2w">

    ```bash {{ title: 'cURL' }}
    curl -X PUT https://api.protocol.chat/v1/messages/SIuAFUNKdSYHZF2w \
      -H "Authorization: Bearer {token}" \
      -d reactions[red_angry_face][]="KateMcCallister"
    ```

    ```js
    import ApiClient from '@example/protocol-api'

    const client = new ApiClient(token)

    await client.messages.update('SIuAFUNKdSYHZF2w', {
        reactions: {
            red_angry_face: ['KateMcCallister']
        }
    })
    ```

    ```python
    from protocol_api import ApiClient

    client = ApiClient(token)

    client.messages.update("SIuAFUNKdSYHZF2w",
        reactions={"red_angry_face": ["KateMcCallister"]})
    ```

    ```php
    $client = new \Protocol\ApiClient($token);

    $client->messages->update('SIuAFUNKdSYHZF2w', [
      'reactions' => [
        'red_angry_face' => ['KateMcCallister'],
      ],
    ]);
    ```

    </CodeGroup>

    ```json {{ title: 'Response' }}
    {
      "id": "SIuAFUNKdSYHZF2w",
      "conversation_id": "xgQQXg3hrtjh7AvZ",
      "contact": {
        "id": "WAz8eIbvDR60rouK",
        "username": "KevinMcCallister",
        "phone_number": "**************",
        "avatar_url": "https://assets.protocol.chat/avatars/buzzboy.jpg",
        "last_active_at": *********,
        "created_at": *********
      },
      "message": "I'm not apologizing. I'd rather kiss a toilet seat.",
      "reactions": [
        {
          "red_angry_face": [
            "KateMcCallister"
          ]
        }
      ],
      "attachments": [],
      "read_at": *********,
      "created_at": *********,
      "updated_at": *********
    }
    ```

  </Col>
</Row>

---

## Delete a message {{ tag: 'DELETE', label: '/v1/messages/:id' }}

<Row>
  <Col>

    This endpoint allows you to delete messages from your conversations. Note: This will permanently delete the message.

  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="DELETE" label="/v1/messages/SIuAFUNKdSYHZF2w">

    ```bash {{ title: 'cURL' }}
    curl -X DELETE https://api.protocol.chat/v1/messages/SIuAFUNKdSYHZF2w \
      -H "Authorization: Bearer {token}"
    ```

    ```js
    import ApiClient from '@example/protocol-api'

    const client = new ApiClient(token)

    await client.messages.delete('SIuAFUNKdSYHZF2w')
    ```

    ```python
    from protocol_api import ApiClient

    client = ApiClient(token)

    client.messages.delete("SIuAFUNKdSYHZF2w")
    ```

    ```php
    $client = new \Protocol\ApiClient($token);

    $client->messages->delete('SIuAFUNKdSYHZF2w');
    ```

    </CodeGroup>

  </Col>
</Row>
