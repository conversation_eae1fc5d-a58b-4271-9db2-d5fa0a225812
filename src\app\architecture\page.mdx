export const metadata = {
  title: 'Architecture',
  description:
    '',
}


## Arquitectura General y Funcionamiento

### **1. AWS Cloud**  
La infraestructura de **Integradoc** está segmentada en dos **VPCs** para garantizar seguridad y escalabilidad:

- **VPC-1 (Private Subnet)**: Aloja el backend, los microservicios y la base de datos.  
- **VPC-2 (Private y Public Subnet)**: Contiene el servidor de aplicación y el balanceador de carga.  

---

### **2. Componentes en VPC-1 (Backend y Middleware)**  

#### **2.1 Microservicios de Integradoc**  
- Procesan la lógica de negocio y gestionan las solicitudes.  
- Se comunican con otros servicios internos y externos.  

#### **2.2 Servicios de Soporte (Elasticsearch y RabbitMQ)**  
- **Elasticsearch**: Motor de búsqueda optimizado para consultas eficientes.  
- **RabbitMQ**: Sistema de mensajería asíncrona para gestionar eventos y procesos desacoplados.  

#### **2.3 Base de Datos de Integradoc (PostgreSQL)**  
- Base de datos principal de la aplicación.  
- Conectada a los microservicios mediante conexiones seguras.  

---

### **3. Componentes en VPC-2 (Exposición de Servicios y Backend)**  

#### **3.1 Servidor de Aplicación de Integradoc**  
- Aloja la aplicación principal.  
- Ubicado en la **subred privada**, accesible solo a través del **Load Balancer**.  

#### **3.2 Application Load Balancer (ALB)**  
- Situado en la **subred pública**.  
- Distribuye el tráfico entrante entre los servidores de aplicación para garantizar disponibilidad y escalabilidad.  

---

## 4. API Gateway y Seguridad

El **API Gateway** de AWS es el punto de entrada central para las integraciones entre los sistemas internos, Integradoc, y servicios externos como Temenos Transact. Se configura cuidadosamente para cumplir con altos estándares de seguridad, rendimiento y trazabilidad.

### Funcionalidades clave

- **Gestión de métodos HTTP**: Se habilitan métodos `GET`, `POST`, `PUT` y `OPTIONS`, según la operación requerida por el flujo del BPM.
- **Validación estricta de entradas**: Cada método está respaldado por una **plantilla de integración (mapping template)** en formato JSON. Estas plantillas definen con precisión los parámetros, headers y body que son aceptados, asegurando que únicamente se procese la información esperada.
- **Seguridad a nivel de API Gateway**:
  - **Rate limiting**: Se configuran límites de solicitudes por segundo y por cuenta (throttle y quota) para evitar sobrecargas o abusos.
  - **Encabezados personalizados**: Se controlan headers como `x-api-key`, `T24-Token`, `User-Agent`, y `X-Forwarded-For` para asegurar la autenticidad, trazabilidad y control de acceso.
  - **CORS habilitado en entorno productivo**: Se permite el acceso a los endpoints desde dominios autorizados del entorno BPM mediante una política CORS controlada, lo que garantiza interoperabilidad sin exponer la API a orígenes no verificados.

---

### 4.1 AWS Lambda Functions

Las funciones Lambda funcionan como **middleware** que encapsula la lógica necesaria para comunicarse con los sistemas externos, aplicando validaciones, transformación de datos y control de errores:

- **Conexión segura con Temenos Transact**: Adaptación de payloads, autenticación con tokens y manejo de respuestas del core bancario.
- **Validación de estructura**: Los datos recibidos se validan y formatean antes de ser reenviados, y las respuestas se limpian para exponer solo la información necesaria al consumidor.

---

### 4.2 Keycloak como Identity Provider (IDP)

El acceso a la plataforma y a las integraciones está protegido mediante tokens JWT firmados por **Keycloak**, configurado como IDP oficial del BPM:

- **JWT Init (RS256)**: Emisión de tokens seguros firmados con algoritmo RS256 para autenticación de usuarios y sistemas.
- **JWT Refresh (RS256)**: Permite renovar los tokens antes de expirar sin requerir nuevas credenciales.
- **Scopes y claims personalizados**: Cada token contiene claims que definen permisos y roles, asegurando un control de acceso granular a nivel de endpoint o recurso.

---

### **5. Seguridad y Red**  

#### **5.1 Fortinet VPN y Active Directory**  
- Manejo de conectividad segura con:  
  - **C&W IaaS** (Infraestructura como Servicio).  
  - **Active Directory** para autenticación de usuarios internos.  
  - **Temenos Cloud** mediante una conexión **VPN segura**.  

#### **5.2 Servicios de Seguridad en AWS**  
- **CloudWatch & CloudTrail**: Monitoreo en tiempo real y auditoría de eventos.  
- **Route 53**: Servicio de DNS y resolución de nombres para infraestructura en AWS.  
- **AWS Shield**: Protección avanzada contra ataques DDoS.  

---

### **6. Integración con Temenos Cloud**  

El flujo de comunicación con **Temenos Transact** se realiza mediante **VPN** y sigue estos pasos:  

1. **Integradoc envía la solicitud HTTP** → API Gateway en AWS.  
2. **AWS Lambda procesa la solicitud** y la reenvía a Temenos a través de la **VPN**.  
3. **Temenos Transact procesa los datos** y devuelve la respuesta.  
4. **La respuesta es enviada de vuelta** a AWS Lambda, API Gateway y finalmente a Integradoc.  

---

### **7. Oficinas y Usuarios Internos**  

- Los usuarios internos acceden a la infraestructura de Integradoc a través de **VPN Fortinet**, asegurando una conexión cifrada y segura.  
- La autenticación y control de accesos se gestionan con **Active Directory** y políticas de seguridad en AWS.  

---