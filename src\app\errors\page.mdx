export const metadata = {
  title: 'Errors',
  description:
    'In this guide, we will talk about what happens when something goes wrong while you work with the API.',
}

# Errores

En esta guía, hablaremos sobre los posibles errores que puedes encontrar al interactuar con la API. Los errores pueden ocurrir por varias razones, generalmente debido a problemas en la solicitud enviada. A continuación, revisaremos los códigos de estado y los tipos de [error](#) que podrías enfrentar. {{ className: 'lead' }}

Puedes verificar si tu solicitud fue exitosa revisando el código de estado en la respuesta de la API. Si la respuesta indica un error, puedes utilizar el tipo de [error](#) y el mensaje de [error](#) para diagnosticar el problema antes de contactar al soporte.

<Note>
  Antes de contactar al soporte, ten en cuenta que la mayoría de los errores reportados son errores de usuario. Por favor, revisa detenidamente tu solicitud antes de solicitar asistencia.
</Note>

---

## Códigos de estado

A continuación, se presentan las distintas categorías de códigos de estado que puede devolver la API. Usa esta información para entender si una solicitud fue procesada correctamente.

<Properties>
  <Property name="2xx">
    Un código de estado 2xx indica que la solicitud se procesó exitosamente.
  </Property>
  <Property name="4xx">
    Un código de estado 4xx indica un [error](#) del cliente — es decir, hay un problema en tu solicitud.
  </Property>
  <Property name="5xx">
    Un código de estado 5xx indica un [error](#) en el servidor — generalmente, estos son problemas internos.
  </Property>
</Properties>

---

## Tipos de errores

<Row>
  <Col>

    Cuando una solicitud no se procesa correctamente, la API Gateway devolverá una respuesta de [error](#) con un tipo de [error](#) y un mensaje descriptivo. Puedes utilizar esta información para entender mejor el problema y cómo solucionarlo. La mayoría de los mensajes de [error](#) son bastante claros y brindan información útil.

    A continuación, se presentan los errores más comunes que podrías encontrar en esta API.

    <Properties>
      <Property name="unauthorized">
        El token JWT enviado es inválido o ha expirado. Asegúrate de generar un nuevo token antes de realizar la solicitud.
      </Property>
      <Property name="missing_token">
        No se ha proporcionado un token en los encabezados de la solicitud. Asegúrate de incluirlo en el header `Authorization: Bearer <token>`.
      </Property>
      <Property name="invalid_signature">
        La firma del token JWT no es válida. Esto suele ocurrir cuando el token ha sido manipulado o la clave secreta utilizada para firmarlo no coincide con la esperada.
      </Property>
      <Property name="forbidden">
        El usuario autenticado no tiene permisos para acceder a este recurso. Revisa las políticas de autorización y los roles asignados.
      </Property>
      <Property name="bad_request">
        La solicitud enviada tiene parámetros incorrectos o incompletos. Verifica que todos los campos requeridos estén presentes y en el formato adecuado.
      </Property>
      <Property name="internal_server_error">
        Se ha producido un [error](#) inesperado en el servidor. Intenta nuevamente más tarde. Si el problema persiste, contacta al soporte técnico.
      </Property>
    </Properties>

  </Col>

  <Col>

    ```bash {{ title: "[error](#) response" }}
    {
      "type": "api_error",
      "message": "No way this is happening!?",
      "documentation_url": "https://privaldocs/apidocs/errors#codigos-de-estado"
    }
    ```

  </Col>
</Row>
