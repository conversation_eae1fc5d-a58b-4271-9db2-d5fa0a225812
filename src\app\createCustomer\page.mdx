export const metadata = {
  title: 'POST Customer',
  description:
    'On this page, we’ll dive into the different customers endpoints you can use to manage customers programmatically.',
}

# Clientes
Los clientes son una parte esencial de la plataforma. Este endpoint te permite crear un nuevo cliente en el core bancario de manera programática a través de una integración con el sistema central.

## Creación del modelo de cliente
  El modelo de cliente contiene toda la información necesaria para registrar correctamente a un cliente en el core bancario. Esto incluye datos personales como nombre completo, dirección, información de contacto, estado civil, ocupación y detalles legales. Todos estos datos deben ser proporcionados correctamente en la solicitud para cumplir con las validaciones del sistema y los requisitos regulatorios. 
  Una vez creado, el cliente será asignado con un ID único que puede utilizarse posteriormente para consultas, actualizaciones o vinculaciones con otros productos financieros dentro del sistema.

---

# Endpoints disponibles de Create Customer

## Customer Create {{ tag: 'POST', label: '/customer' }}

<Row>
  <Col>

    Este endpoint crea un cliente en el core bancario.

    ### Required attributes

    <Properties>
      <Property name="Body" type="string">
        Todo el cuerpo de la solicitud es requerido
      </Property>
    </Properties>

  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="POST" label="/customer">

    ```bash {{ title: 'cURL' }}
      curl --location 'https://ztr3w5aj41.execute-api.us-east-1.amazonaws.com/QA/customer' \
      --header 'T24-Token: T24-Token' \
      --header 'x-api-key: xXlqWn4cPp9IQhkjVFkgbvJeauo9rN719dduKX80' \
      --header 'Content-Type: application/json' \
      --data-raw '{
        "title": "MR",
        "givenName": "John",
        "familyName": "Smith",
        "manualRiskClass": "MR",
        "sectorId": 1201,
        "addresses": [
          {
            "address": "Torre Oceanía, Piso 12"
          }
        ],
        "streets": [
          {
            "street": "Avenida Balboa"
          }
        ],
        "contactDate": "2022-05-14",
        "nationalityId": "PA",
        "displayNames": [
          {
            "displayName": "Mr. John Smith"
          }
        ],
        "customerNames": [
          {
            "customerName": "Mr. John Smith"
          }
        ],
        "language": 2,
        "countries": [
          {
            "country": "PA"
          }
        ],
        "communicationDevices": [
          {
            "phoneNumber": "61234567",
            "smsNumber": "60098765",
            "preferredChannel": "EMAIL",
            "email": "<EMAIL>",
            "communicationType": "NEW.PRODUCT.UPDATE"
          }
        ],
        "officePhoneNumbers": [
          {
            "officePhoneNumber": "2023456"
          }
        ],
        "employDetails": [
          {
            "occupation": "Consultor Financiero",
            "employStatus": "EMPLOYED",
            "salaryAmount": 8500,
            "salaryCurrency": "USD",
            "employStartDate": "2019-03-01"
          }
        ],
        "relationshipDetails": [
          {
            "reverseJointRelationCode": 22,
            "jointCustomer": 112345,
            "jointRelationCode": 23
          }
        ],
        "numberOfDependents": 2,
        "amlCheck": "NULL",
        "gender": "MALE",
        "residenceId": "PA",
        "target": 850,
        "customerStatus": 1,
        "industryId": 1103,
        "accountOfficerId": 1001,
        "dateOfBirth": "1985-08-12",
        "maritalStatus": "MARRIED",
        "birthIncorpDate": "1985-08-12",
        "customerCompany": "PA0010001",
        "amlResult": "NULL",
        "overrideReason": "Reviewed for new product onboarding",
        "legalDetails": [
          {
            "legalIssueDate": "2015-06-20",
            "legalExpiredDate": "2025-06-20",
            "legalId": "PA-800812-0012",
            "legalDocumentName": "CI",
            "legalIssueAuthorisedDate": "PANAMA",
            "legalHolderName": "John Smith"
          }
        ]
      }
      '
    ```

    ```js
      const myHeaders = new Headers();
      myHeaders.append("T24-Token", "{{T24-Token}}");
      myHeaders.append("x-api-key", "xXlqWn4cPp9IQhkjVFkgbvJeauo9rN719dduKX80");
      myHeaders.append("Content-Type", "application/json");

      const raw = JSON.stringify({
        "title": "MR",
        "givenName": "John",
        "familyName": "Smith",
        "manualRiskClass": "MR",
        "sectorId": 1201,
        "addresses": [
          {
            "address": "Torre Oceanía, Piso 12"
          }
        ],
        "streets": [
          {
            "street": "Avenida Balboa"
          }
        ],
        "contactDate": "2022-05-14",
        "nationalityId": "PA",
        "displayNames": [
          {
            "displayName": "Mr. John Smith"
          }
        ],
        "customerNames": [
          {
            "customerName": "Mr. John Smith"
          }
        ],
        "language": 2,
        "countries": [
          {
            "country": "PA"
          }
        ],
        "communicationDevices": [
          {
            "phoneNumber": "61234567",
            "smsNumber": "60098765",
            "preferredChannel": "EMAIL",
            "email": "<EMAIL>",
            "communicationType": "NEW.PRODUCT.UPDATE"
          }
        ],
        "officePhoneNumbers": [
          {
            "officePhoneNumber": "2023456"
          }
        ],
        "employDetails": [
          {
            "occupation": "Consultor Financiero",
            "employStatus": "EMPLOYED",
            "salaryAmount": 8500,
            "salaryCurrency": "USD",
            "employStartDate": "2019-03-01"
          }
        ],
        "relationshipDetails": [
          {
            "reverseJointRelationCode": 22,
            "jointCustomer": 112345,
            "jointRelationCode": 23
          }
        ],
        "numberOfDependents": 2,
        "amlCheck": "NULL",
        "gender": "MALE",
        "residenceId": "PA",
        "target": 850,
        "customerStatus": 1,
        "industryId": 1103,
        "accountOfficerId": 1001,
        "dateOfBirth": "1985-08-12",
        "maritalStatus": "MARRIED",
        "birthIncorpDate": "1985-08-12",
        "customerCompany": "PA0010001",
        "amlResult": "NULL",
        "overrideReason": "Reviewed for new product onboarding",
        "legalDetails": [
          {
            "legalIssueDate": "2015-06-20",
            "legalExpiredDate": "2025-06-20",
            "legalId": "PA-800812-0012",
            "legalDocumentName": "CI",
            "legalIssueAuthorisedDate": "PANAMA",
            "legalHolderName": "John Smith"
          }
        ]
      });

      const requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: raw,
        redirect: "follow"
      };

      fetch("https://ztr3w5aj41.execute-api.us-east-1.amazonaws.com/QA/customer", requestOptions)
        .then((response) => response.text())
        .then((result) => console.log(result))
        .catch((error) => console.error(error));
    ```

    ```python
    import requests
    import json

    url = "https://ztr3w5aj41.execute-api.us-east-1.amazonaws.com/QA/customer"

    payload = json.dumps({
      "title": "MR",
      "givenName": "John",
      "familyName": "Smith",
      "manualRiskClass": "MR",
      "sectorId": 1201,
      "addresses": [
        {
          "address": "Torre Oceanía, Piso 12"
        }
      ],
      "streets": [
        {
          "street": "Avenida Balboa"
        }
      ],
      "contactDate": "2022-05-14",
      "nationalityId": "PA",
      "displayNames": [
        {
          "displayName": "Mr. John Smith"
        }
      ],
      "customerNames": [
        {
          "customerName": "Mr. John Smith"
        }
      ],
      "language": 2,
      "countries": [
        {
          "country": "PA"
        }
      ],
      "communicationDevices": [
        {
          "phoneNumber": "61234567",
          "smsNumber": "60098765",
          "preferredChannel": "EMAIL",
          "email": "<EMAIL>",
          "communicationType": "NEW.PRODUCT.UPDATE"
        }
      ],
      "officePhoneNumbers": [
        {
          "officePhoneNumber": "2023456"
        }
      ],
      "employDetails": [
        {
          "occupation": "Consultor Financiero",
          "employStatus": "EMPLOYED",
          "salaryAmount": 8500,
          "salaryCurrency": "USD",
          "employStartDate": "2019-03-01"
        }
      ],
      "relationshipDetails": [
        {
          "reverseJointRelationCode": 22,
          "jointCustomer": 112345,
          "jointRelationCode": 23
        }
      ],
      "numberOfDependents": 2,
      "amlCheck": "NULL",
      "gender": "MALE",
      "residenceId": "PA",
      "target": 850,
      "customerStatus": 1,
      "industryId": 1103,
      "accountOfficerId": 1001,
      "dateOfBirth": "1985-08-12",
      "maritalStatus": "MARRIED",
      "birthIncorpDate": "1985-08-12",
      "customerCompany": "PA0010001",
      "amlResult": "NULL",
      "overrideReason": "Reviewed for new product onboarding",
      "legalDetails": [
        {
          "legalIssueDate": "2015-06-20",
          "legalExpiredDate": "2025-06-20",
          "legalId": "PA-800812-0012",
          "legalDocumentName": "CI",
          "legalIssueAuthorisedDate": "PANAMA",
          "legalHolderName": "John Smith"
        }
      ]
    })
    headers = {
      'T24-Token': '{{T24-TOKEN}}',
      'x-api-key': 'xXlqWn4cPp9IQhkjVFkgbvJeauo9rN719dduKX80',
      'Content-Type': 'application/json'
    }

    response = requests.request("POST", url, headers=headers, data=payload)

    print(response.text)

    ```
    </CodeGroup>

  </Col>
</Row>

  ---
  # Seguridad y Configuración del Endpoint POST /customer

  Esta sección describe cómo se ha configurado el **API Gateway** que expone el endpoint de creacion de clientes, garantizando un manejo seguro, eficiente y controlado de las solicitudes entrantes.

  ---

  ## 🔐 Seguridad: CORS y Encabezados Personalizados

  ### ✅ Habilitación de CORS

  Para permitir el consumo del API desde aplicaciones web o herramientas externas, se habilita **CORS** en el método `POST`:

  - `Access-Control-Allow-Origin: BPM`  
    (limitado a nuestro entorno productivo de nuestro BPM).
  - `Access-Control-Allow-Methods: POST, OPTIONS`
  - `Access-Control-Allow-Headers: Content-Type, Authorization, T24-Token, x-api-key`

  Esto permite que navegadores modernos interactúen con el API sin restricciones innecesarias, respetando los estándares web.

  ---

  ### 🧾 Encabezados Personalizados

  Para reforzar la seguridad y trazabilidad, se procesan y validan los siguientes encabezados:

  - `T24-Token`: Token requerido para autenticar contra el core bancario.
  - `x-api-key`: Clave de API administrada desde API Gateway.
  - `User-Agent`, `Accept`, `Host`: Información básica de cliente.
  - `X-Forwarded-For`, `X-Forwarded-Port`, `X-Forwarded-Proto`: Información de red para auditoría.
  - `X-Amzn-Trace-Id`: ID de trazabilidad generado por AWS.

  ---

  ## ⚙️ Plantilla de Integración (Mapping Template)

  Para proteger la función Lambda y controlar la entrada de datos, se usa una plantilla personalizada en formato **VTL (Velocity Template Language)**. Esta plantilla transforma la solicitud HTTP en un JSON estructurado, asegurando que solo se transmita la información permitida.

  ### 🧩 Plantilla de ejemplo – POST /customer

  ```json
  {
    "queryStringParameters": {
      #en este caso no es posible enviar query params
    },
    "body": $input.body,
    "headers": {
      "Accept": "$input.params().header.Accept",
      "Accept-Encoding": "$input.params().header.Accept-Encoding",
      "Cache-Control": "$input.params().header.Cache-Control",
      "Host": "$input.params().header.Host",
      "User-Agent": "$input.params().header.User-Agent",
      "X-Amzn-Trace-Id": "$input.params().header.X-Amzn-Trace-Id",
      "x-api-key": "$input.params().header.x-api-key",
      "T24-Token": "$input.params().header.T24-Token",
      "X-Forwarded-For": "$context.identity.sourceIp",
      "X-Forwarded-Port": "$context.identity.sourcePort",
      "X-Forwarded-Proto": "$context.protocol"
    }
  }
  ```
---

  ## ✅ Respuesta Exitosa

```json {{ title: 'Response' }}
    {
      {
        "statusCode": 200,
        "id": "*******"
      }
    }
    ```