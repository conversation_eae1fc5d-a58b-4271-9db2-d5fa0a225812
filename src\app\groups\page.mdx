export const metadata = {
  title: 'Groups',
  description:
    'On this page, we’ll dive into the different group endpoints you can use to manage groups programmatically.',
}

# Groups

Groups are where communities live in Protocol — they are a collection of contacts you're talking to all at once. On this page, we'll dive into the different group endpoints you can use to manage groups programmatically. We'll look at how to query, create, update, and delete groups. {{ className: 'lead' }}

## The group model

The group model contains all the information about your groups, including what contacts are in the group and the group's name, description, and avatar.

### Properties

<Properties>
  <Property name="id" type="string">
    Unique identifier for the group.
  </Property>
  <Property name="name" type="string">
    The name for the group.
  </Property>
  <Property name="description" type="string">
    The description for the group.
  </Property>
  <Property name="avatar_url" type="string">
    The avatar image URL for the group.
  </Property>
  <Property name="conversation_id" type="string">
    Unique identifier for the conversation that belongs to the group.
  </Property>
  <Property name="contacts" type="array">
    An array of contact objects that are members of the group.
  </Property>
  <Property name="created_at" type="timestamp">
    Timestamp of when the group was created.
  </Property>
  <Property name="archived_at" type="timestamp">
    Timestamp of when the group was archived.
  </Property>
</Properties>

---

## List all groups {{ tag: 'GET', label: '/v1/groups' }}

<Row>
  <Col>

    This endpoint allows you to retrieve a paginated list of all your groups. By default, a maximum of ten groups are shown per page.

    ### Optional attributes

    <Properties>
      <Property name="limit" type="integer">
        Limit the number of groups returned.
      </Property>
      <Property name="archived" type="boolean">
        Only show groups that are archived when set to `true`.
      </Property>
    </Properties>

  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="GET" label="/v1/groups">

    ```bash {{ title: 'cURL' }}
    curl -G https://api.protocol.chat/v1/groups \
      -H "Authorization: Bearer {token}" \
      -d limit=10
    ```

    ```js
    import ApiClient from '@example/protocol-api'

    const client = new ApiClient(token)

    await client.groups.list()
    ```

    ```python
    from protocol_api import ApiClient

    client = ApiClient(token)

    client.groups.list()
    ```

    ```php
    $client = new \Protocol\ApiClient($token);

    $client->groups->list();
    ```

    </CodeGroup>

    ```json {{ title: 'Response' }}
    {
      "has_more": false,
      "data": [
        {
          "id": "l7cGNIBKZiNJ6wqF",
          "name": "Plaza Hotel",
          "description": "World-renowned.",
          "avatar_url": "https://assets.protocol.chat/avatars/plazahotel.jpg",
          "conversation_id": "ZYjVAbCE9g5XRlra",
          "contacts": [
            {
              "username": "Hector"
              // ...
            },
            {
              "username": "Cedric"
              // ...
            },
            {
              "username": "Hester"
              // ...
            },
            {
              "username": "Cliff"
              // ...
            }
          ],
          "created_at": 692233200,
          "archived_at": null
        },
        {
          "id": "hSIhXBhNe8X1d8Et"
          // ...
        }
      ]
    }
    ```

  </Col>
</Row>

---

## Create a group {{ tag: 'POST', label: '/v1/groups' }}

<Row>
  <Col>

    This endpoint allows you to create a new group conversation between you and a group of your Protocol contacts.

    ### Required attributes

    <Properties>
      <Property name="name" type="string">
        The name for the group.
      </Property>
    </Properties>

    ### Optional attributes

    <Properties>
      <Property name="description" type="string">
        The description for the group.
      </Property>
      <Property name="avatar_url" type="string">
        The avatar image URL for the group.
      </Property>
      <Property name="contacts" type="array">
        An array of contact objects that are members of the group.
      </Property>
    </Properties>

  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="POST" label="/v1/groups">

    ```bash {{ title: 'cURL' }}
    curl https://api.protocol.chat/v1/groups \
      -H "Authorization: Bearer {token}" \
      -d name="Plaza Hotel"
    ```

    ```js
    import ApiClient from '@example/protocol-api'

    const client = new ApiClient(token)

    await client.groups.create({
      name: 'Plaza Hotel',
    })
    ```

    ```python
    from protocol_api import ApiClient

    client = ApiClient(token)

    client.groups.create(name="Plaza Hotel")
    ```

    ```php
    $client = new \Protocol\ApiClient($token);

    $client->groups->create([
      'name' => 'Plaza Hotel',
    ]);
    ```

    </CodeGroup>

    ```json {{ title: 'Response' }}
    {
      "id": "l7cGNIBKZiNJ6wqF",
      "name": "Plaza Hotel",
      "description": null,
      "avatar_url": null,
      "conversation_id": "ZYjVAbCE9g5XRlra",
      "contacts": [],
      "created_at": 692233200,
      "archived_at": null
    }
    ```

  </Col>
</Row>

---

## Retrieve a group {{ tag: 'GET', label: '/v1/groups/:id' }}

<Row>
  <Col>

    This endpoint allows you to retrieve a group by providing the group id. Refer to [the list](#the-group-model) at the top of this page to see which properties are included with group objects.

  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="GET" label="/v1/groups/L7cGNIBKZiNJ6wqF">

    ```bash {{ title: 'cURL' }}
    curl https://api.protocol.chat/v1/groups/L7cGNIBKZiNJ6wqF \
      -H "Authorization: Bearer {token}"
    ```

    ```js
    import ApiClient from '@example/protocol-api'

    const client = new ApiClient(token)

    await client.groups.get('L7cGNIBKZiNJ6wqF')
    ```

    ```python
    from protocol_api import ApiClient

    client = ApiClient(token)

    client.groups.get("L7cGNIBKZiNJ6wqF")
    ```

    ```php
    $client = new \Protocol\ApiClient($token);

    $client->groups->get('L7cGNIBKZiNJ6wqF');
    ```

    </CodeGroup>

    ```json {{ title: 'Response' }}
    {
      "id": "l7cGNIBKZiNJ6wqF",
      "name": "Plaza Hotel",
      "description": "World-renowned.",
      "avatar_url": "https://assets.protocol.chat/avatars/plazahotel.jpg",
      "conversation_id": "ZYjVAbCE9g5XRlra",
      "contacts": [
        {
          "username": "Hector"
          // ...
        },
        {
          "username": "Cedric"
          // ...
        },
        {
          "username": "Hester"
          // ...
        },
        {
          "username": "Cliff"
          // ...
        }
      ],
      "created_at": 692233200,
      "archived_at": null
    }
    ```

  </Col>
</Row>

---

## Update a group {{ tag: 'PUT', label: '/v1/groups/:id' }}

<Row>
  <Col>

    This endpoint allows you to perform an update on a group. Examples of updates are changing the name, description, and avatar or adding and removing contacts from the group.

    ### Optional attributes

    <Properties>
      <Property name="name" type="string">
        The new name for the group.
      </Property>
      <Property name="description" type="string">
        The new description for the group.
      </Property>
      <Property name="avatar_url" type="string">
        The new avatar image URL for the group.
      </Property>
      <Property name="contacts" type="array">
        An array of contact objects that are members of the group.
      </Property>
      <Property name="archived_at" type="timestamp">
        Timestamp of when the group was archived.
      </Property>
    </Properties>

  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="PUT" label="/v1/groups/L7cGNIBKZiNJ6wqF">

    ```bash {{ title: 'cURL' }}
    curl -X PUT https://api.protocol.chat/v1/groups/L7cGNIBKZiNJ6wqF \
      -H "Authorization: Bearer {token}" \
      -d description="The finest in New York."
    ```

    ```js
    import ApiClient from '@example/protocol-api'

    const client = new ApiClient(token)

    await client.groups.update('L7cGNIBKZiNJ6wqF', {
        description: 'The finest in New York.',
    })
    ```

    ```python
    from protocol_api import ApiClient

    client = ApiClient(token)

    client.groups.update("L7cGNIBKZiNJ6wqF", description="The finest in New York.")
    ```

    ```php
    $client = new \Protocol\ApiClient($token);

    $client->groups->update('L7cGNIBKZiNJ6wqF', [
      'description' => 'The finest in New York.',
    ]);
    ```

    </CodeGroup>

    ```json {{ title: 'Response' }}
    {
      "id": "l7cGNIBKZiNJ6wqF",
      "name": "Plaza Hotel",
      "description": "The finest in New York.",
      "avatar_url": "https://assets.protocol.chat/avatars/plazahotel.jpg",
      "conversation_id": "ZYjVAbCE9g5XRlra",
      "contacts": [
        {
          "username": "Hector"
          // ...
        },
        {
          "username": "Cedric"
          // ...
        },
        {
          "username": "Hester"
          // ...
        },
        {
          "username": "Cliff"
          // ...
        }
      ],
      "created_at": 692233200,
      "archived_at": null
    },
    ```

  </Col>
</Row>

---

## Delete a group {{ tag: 'DELETE', label: '/v1/groups/:id' }}

<Row>
  <Col>

    This endpoint allows you to delete groups. Note: This will permanently delete the group, including the messages — archive it instead if you want to be able to restore it later.

  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="DELETE" label="/v1/groups/L7cGNIBKZiNJ6wqF">

    ```bash {{ title: 'cURL' }}
    curl -X DELETE https://api.protocol.chat/v1/groups/L7cGNIBKZiNJ6wqF \
      -H "Authorization: Bearer {token}"
    ```

    ```js
    import ApiClient from '@example/protocol-api'

    const client = new ApiClient(token)

    await client.groups.delete('L7cGNIBKZiNJ6wqF')
    ```

    ```python
    from protocol_api import ApiClient

    client = ApiClient(token)

    client.groups.delete("L7cGNIBKZiNJ6wqF")
    ```

    ```php
    $client = new \Protocol\ApiClient($token);

    $client->groups->delete('L7cGNIBKZiNJ6wqF');
    ```

    </CodeGroup>

  </Col>
</Row>
